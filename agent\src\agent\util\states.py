from typing import Optional, Sequence
from typing_extensions import Annotated
from pydantic import BaseModel, Field


from langchain_core.messages import BaseMessage

from langgraph.managed import RemainingSteps

from agent.util.message_adder import add

class OfferParams(BaseModel):
    country: Optional[str] = None
    subscription: Optional[str] = None
    term: Optional[str] = None
    seats: Optional[int] = None

class Offer(OfferParams):
    price: Optional[float] = None

class State(BaseModel):
    messages: Annotated[Sequence[BaseMessage], add]
    remaining_steps: RemainingSteps = 25
    offer: Offer = Field(default_factory = Offer)
    