from langgraph.graph.message import add_messages

from agent.util.messages_tool_call_remover import remove_tool_calls, summarize_chat_history

def add(left, right):
        """
        This function is used to add messages to the state.
        In this case, it also removes tool calls from the messages backwards.
        THIS IS A REALLY BAD PRACTICE, BECAUSE IT HAVE A SIDE EFFECTS.
        IT WOULD BE BETTER TO USE A PRE-MODEL HOOK TO REMOVE TOOL CALLS. 
        BUT WITH THE CURRENT AGENT-CHAT-UI AND LANGRAPH API IF I USE A PRE-MODEL HOOK THE CHAT HISTORY DISPLAY START TO DISPLAY MESSAGES TWICE, AND REDRAWING THE WHOLE CHAT_HISTORY FULLY WHEN UPDATE.
        
        IF YOU KNOW HOW TO FIX THIS IN CHAT UI, PLEASE LET ME KNOW.
        IF CHANGE CHAT UI CHANGE THIS FUNCTION AND MAKE A PRE-M<PERSON><PERSON> HOOK TO REMOVE TOOL CALLS.

        """

        added_messages = add_messages(left, right)

        print(f"LEFT messages: {left}")
        print("-------------------------------------------------------------------------------")
        print(f"RIGHT messages: {right}")
        print("-------------------------------------------------------------------------------")
        print(f"Size of LEFT: {len(left) if left else -1}")
        print(f"Size of RIGHT: {len(right) if right else -1}")

        # Generate and print chat history summary
        if added_messages:
            summary = summarize_chat_history(added_messages, max_length=300, include_tool_calls=True)
            print(f"CHAT SUMMARY: {summary}")
            print("===============================================================================")

        added_messages = remove_tool_calls(added_messages, save_last_message_tool_call=3)


        return added_messages