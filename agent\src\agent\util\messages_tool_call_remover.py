from langchain_core.messages import BaseMessage
from typing import Sequence, List, Dict, Any

def remove_tool_calls(
        messages: Sequence[BaseMessage],
        save_last_message_tool_call: int = 0 # Number of last responses when tool called to save the tool calls in. 0 means delete all as soon as possible. Negative value means save all tool calls (default: 0)
        ) -> Sequence[BaseMessage]:
    
    if messages[-1].type == "tool" or save_last_message_tool_call < 0:
        return messages

    tool_call_removed_messages = []

    # keep_processing = True
    response_with_tool_call_num = 0
    response_temp_index=0
    last_tool_called_index=-1

    for message in reversed(messages):

        tool_calls = []
        if hasattr(message, 'tool_calls'):
            tool_calls = message.tool_calls

        if response_temp_index > 0  and last_tool_called_index != response_temp_index and message.type == "tool":
            response_with_tool_call_num += 1
            last_tool_called_index = response_temp_index


        if not ( response_with_tool_call_num > save_last_message_tool_call and (message.type == "tool" or tool_calls != []) ) :
            tool_call_removed_messages.insert(0, message)

        if message.type == "human":
            response_temp_index += 1

    return list(tool_call_removed_messages)


def summarize_chat_history(
    messages: Sequence[BaseMessage],
    max_length: int = 200
) -> str:
    """
    Creates a concise summary of the chat history for LLM context.
    Only includes human and AI messages, excluding tool calls.

    Args:
        messages: Sequence of BaseMessage objects to summarize
        max_length: Maximum length of the summary (default: 200 characters)

    Returns:
        str: A concise conversation summary suitable for LLM context
    """
    if not messages:
        return ""

    # Filter and collect only human and AI messages
    conversation_messages = []

    for message in messages:
        if message.type == "human":
            content = _extract_content(message)
            if content and len(content.strip()) > 0:
                conversation_messages.append(f"User: {content}")

        elif message.type == "ai":
            content = _extract_content(message)
            if content and len(content.strip()) > 0:
                conversation_messages.append(f"Assistant: {content}")

    if not conversation_messages:
        return ""

    # Create summary focusing on the most recent exchanges
    # Take more recent messages if they're short, fewer if they're long
    summary_text = ""
    messages_included = 0

    # Start from the most recent and work backwards
    for message in reversed(conversation_messages):
        test_summary = f"{message} | {summary_text}" if summary_text else message

        if len(test_summary) <= max_length:
            summary_text = test_summary
            messages_included += 1
        else:
            break

    # If we couldn't fit even one message, truncate the most recent one
    if not summary_text and conversation_messages:
        latest_message = conversation_messages[-1]
        summary_text = latest_message[:max_length-3] + "..." if len(latest_message) > max_length else latest_message

    return summary_text


def _extract_content(message: BaseMessage) -> str:
    """
    Helper function to extract readable content from a message.

    Args:
        message: BaseMessage to extract content from

    Returns:
        str: Extracted content as string
    """
    if hasattr(message, 'content'):
        content = message.content

        # Handle string content
        if isinstance(content, str):
            return content.strip()

        # Handle list/complex content (multimodal)
        elif isinstance(content, list):
            text_parts = []
            for item in content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts).strip()

        # Handle other content types
        else:
            return str(content).strip()

    return ""