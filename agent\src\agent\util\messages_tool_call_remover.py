from langchain_core.messages import BaseMessage
from typing import Sequence, List, Dict, Any

def remove_tool_calls(
        messages: Sequence[BaseMessage],
        save_last_message_tool_call: int = 0 # Number of last responses when tool called to save the tool calls in. 0 means delete all as soon as possible. Negative value means save all tool calls (default: 0)
        ) -> Sequence[BaseMessage]:
    
    if messages[-1].type == "tool" or save_last_message_tool_call < 0:
        return messages

    tool_call_removed_messages = []

    # keep_processing = True
    response_with_tool_call_num = 0
    response_temp_index=0
    last_tool_called_index=-1

    for message in reversed(messages):

        tool_calls = []
        if hasattr(message, 'tool_calls'):
            tool_calls = message.tool_calls

        if response_temp_index > 0  and last_tool_called_index != response_temp_index and message.type == "tool":
            response_with_tool_call_num += 1
            last_tool_called_index = response_temp_index


        if not ( response_with_tool_call_num > save_last_message_tool_call and (message.type == "tool" or tool_calls != []) ) :
            tool_call_removed_messages.insert(0, message)

        if message.type == "human":
            response_temp_index += 1

    return list(tool_call_removed_messages)


def summarize_chat_history(
    messages: Sequence[BaseMessage],
    max_length: int = 200
) -> str:
    """
    Creates a short summarization of the chat history.

    Args:
        messages: Sequence of BaseMessage objects to summarize
        max_length: Maximum length of the summary (default: 200 characters)

    Returns:
        str: A concise summary of the chat history
    """
    if not messages:
        return ""

    summary_parts = []
    human_count = 0
    ai_count = 0
    tool_count = 0

    # Count message types and collect key content
    recent_messages = []

    for message in messages:
        if message.type == "human":
            human_count += 1
            content = _extract_content(message)
            if content and len(content.strip()) > 0:
                recent_messages.append(f"User: {content[:100]}...")

        elif message.type == "ai":
            ai_count += 1
            content = _extract_content(message)
            if content and len(content.strip()) > 0:
                recent_messages.append(f"Assistant: {content[:100]}...")

    # Build summary header
    summary_parts.append(f"Chat with {human_count} user message(s), {ai_count} assistant response(s)")
    if tool_count > 0 and include_tool_calls:
        summary_parts.append(f", {tool_count} tool call(s)")

    # Add recent conversation snippet
    if recent_messages:
        # Take last 2-3 exchanges to show recent context
        recent_snippet = " | ".join(recent_messages[-4:])  # Last 4 messages
        summary_parts.append(f". Recent: {recent_snippet}")

    # Join and truncate to max_length
    full_summary = "".join(summary_parts)

    if len(full_summary) > max_length:
        return full_summary[:max_length-3] + "..."

    return full_summary


def _extract_content(message: BaseMessage) -> str:
    """
    Helper function to extract readable content from a message.

    Args:
        message: BaseMessage to extract content from

    Returns:
        str: Extracted content as string
    """
    if hasattr(message, 'content'):
        content = message.content

        # Handle string content
        if isinstance(content, str):
            return content.strip()

        # Handle list/complex content (multimodal)
        elif isinstance(content, list):
            text_parts = []
            for item in content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts).strip()

        # Handle other content types
        else:
            return str(content).strip()

    return ""