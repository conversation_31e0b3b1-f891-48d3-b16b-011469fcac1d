import os
import time
from datetime import datetime
from typing import Any

from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.callbacks import Callbacks
from langchain.schema.runnable import RunnableConfig
from langchain_core.messages.utils import count_tokens_approximately

from langmem.short_term import SummarizationNode

from contextlib import asynccontextmanager

from galileo import galileo_context
from galileo.handlers.langchain import GalileoAsyncCallback

from agent.util.prompt import PROMPT
from agent.util.tools import TOOLS
from agent.util.states import State
from langchain.prompts import ChatPromptTemplate

#Initialize the Galileo callback handler
external_id = f"sales-agent-{int(time.time())}"
current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
session_name = f"Sales Agent test - {current_time}"
galileo_context.start_session(name=session_name, external_id=external_id)
galileo_callback = GalileoAsyncCallback()

model = init_chat_model("google_genai:gemini-2.0-flash")
summarization_model = model.bind(max_tokens=1000)

DEFAULT_INITIAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        ("user", "Create a summary of the conversation above, only answer is the summarization, i don't want any preceeding or succeeding text! If there was a question in the new messages, don't answer it, just add it to the summary! If there was an answer in the new messages, add it to the summary!"),
    ]
)


DEFAULT_EXISTING_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "This is summary of the conversation so far: {existing_summary}\n\n"
            "Extend this summary by taking into account the new messages above, only answer is the summarization, i don't want any preceeding or succeeding text! If there was a question in the new messages, don't answer it, just add it to the summary! If there was an answer in the new messages, add it to the summary!",
        ),
    ]
)

DEFAULT_FINAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        # if exists
        ("placeholder", "{system_message}"),
        ("system", "Summary of the conversation so far: {summary}"),
        ("placeholder", "{messages}"),
    ]
)


@asynccontextmanager
async def make_graph():
   
    mcp_client = MultiServerMCPClient(
        {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp/",
                "transport": "streamable_http",
            },
            "search": {
                "url": "http://mcp-search:8000/mcp/",
                "transport": "streamable_http",
            }
        }
    )

    

    # Gather all tools for the agent
    tools = []
    tools.extend(TOOLS)

    pricing_tools = await mcp_client.get_tools(server_name="pricing")
    tools.extend(pricing_tools)

    search_tools = await mcp_client.get_tools(server_name="search")
    tools.extend(search_tools)

    #model_name = os.environ.get("CHAT_MODEL")
    chat_model = init_chat_model("azure_openai:gpt-4.1")
    #chat_model = init_chat_model("mistralai:mistral-small-2506")

    agent = create_react_agent(
        model = chat_model,
        tools = tools,
        prompt = PROMPT,
        state_schema = State,
    )

    config: dict[str, Any] = {"configurable": {"thread_id": external_id}}
    callbacks: Callbacks = [galileo_callback]
    runnable_config = RunnableConfig(callbacks=callbacks, **config)

    # Configure the agent with the default config
    agent = agent.with_config(runnable_config)

    yield agent


